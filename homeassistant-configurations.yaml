# Loads default set of integrations. Do not remove.
default_config:

# Load frontend themes from the themes folder
frontend:
  themes: !include_dir_merge_named themes

# Text to speech
tts:
  - platform: google_translate

automation: !include automations.yaml
script: !include scripts.yaml
scene: !include scenes.yaml

# Template sensors for human-readable status descriptions
template:
  - sensor:
      #- platform: integration
      #source: sensor.grid_ac_power # Replace with the actual entity ID of your speed sensor
      #name: "grid_ac_power_total"
      #unit_of_measurement: "w" # Or "m" depending on your speed sensor's unit
      #method: left # Or right, trapezoidal. Left is usually sufficient for regular updates.
      #unit_prefix: k # Use 'k' if your speed is in m/s and you want km, or omit if speed is already km/h and you want km
      #round: 2 # Optional: Round the output to 2 decimal places
      # Inverter Status Template Sensors
      - name: "Inv1 Inverter Status Description"
        unique_id: inv1_inverter_status_description
        state: >
          {% set status = states('sensor.inv1_inverter_status') | int(0) %}
          {% if status == 1024 %}Invert
          {% elif status == 1025 %}AC Pass Through
          {% elif status == 1026 %}APS Only
          {% elif status == 1027 %}Load Sense
          {% elif status == 1028 %}Inverter Disabled
          {% elif status == 1029 %}Load Sense Ready
          {% elif status == 1030 %}Engaging Inverter
          {% elif status == 1031 %}Invert Fault
          {% elif status == 1032 %}Inverter Standby
          {% elif status == 1033 %}Grid-Tied
          {% elif status == 1034 %}Grid Support
          {% elif status == 1035 %}Gen Support
          {% elif status == 1036 %}Sell-to-Grid
          {% elif status == 1037 %}Load Shaving
          {% elif status == 1038 %}Grid Frequency Stabilization
          {% elif status == 1039 %}AC Coupling
          {% elif status == 1040 %}Reverse Ibatt
          {% else %}Unknown ({{ status }})
          {% endif %}
        icon: mdi:power-plug
        availability: "{{ states('sensor.inv1_inverter_status') not in ['unknown', 'unavailable'] }}"

      - name: "Inv2 Inverter Status Description"
        unique_id: inv2_inverter_status_description
        state: >
          {% set status = states('sensor.inv2_inverter_status') | int(0) %}
          {% if status == 1024 %}Invert
          {% elif status == 1025 %}AC Pass Through
          {% elif status == 1026 %}APS Only
          {% elif status == 1027 %}Load Sense
          {% elif status == 1028 %}Inverter Disabled
          {% elif status == 1029 %}Load Sense Ready
          {% elif status == 1030 %}Engaging Inverter
          {% elif status == 1031 %}Invert Fault
          {% elif status == 1032 %}Inverter Standby
          {% elif status == 1033 %}Grid-Tied
          {% elif status == 1034 %}Grid Support
          {% elif status == 1035 %}Gen Support
          {% elif status == 1036 %}Sell-to-Grid
          {% elif status == 1037 %}Load Shaving
          {% elif status == 1038 %}Grid Frequency Stabilization
          {% elif status == 1039 %}AC Coupling
          {% elif status == 1040 %}Reverse Ibatt
          {% else %}Unknown ({{ status }})
          {% endif %}
        icon: mdi:power-plug
        availability: "{{ states('sensor.inv2_inverter_status') not in ['unknown', 'unavailable'] }}"

      # Charger Status Template Sensors
      - name: "Inv1 Charger Status Description"
        unique_id: inv1_charger_status_description
        state: >
          {% set status = states('sensor.inv1_charger_status') | int(0) %}
          {% if status == 768 %}Not Charging
          {% elif status == 769 %}Bulk
          {% elif status == 770 %}Absorption
          {% elif status == 771 %}Overcharge
          {% elif status == 772 %}Equalize
          {% elif status == 773 %}Float
          {% elif status == 774 %}No Float
          {% elif status == 775 %}Constant VI
          {% elif status == 776 %}Charger Disabled
          {% elif status == 777 %}Qualifying AC
          {% elif status == 778 %}Qualifying APS
          {% elif status == 779 %}Engaging Charger
          {% elif status == 780 %}Charge Fault
          {% elif status == 781 %}Charger Suspend
          {% elif status == 782 %}AC Good
          {% elif status == 783 %}APS Good
          {% elif status == 784 %}AC Fault
          {% elif status == 785 %}Charge
          {% elif status == 786 %}Absorption Exit Pending
          {% elif status == 787 %}Ground Fault
          {% elif status == 788 %}AC Good Pending
          {% else %}Unknown ({{ status }})
          {% endif %}
        icon: mdi:battery-charging
        availability: "{{ states('sensor.inv1_charger_status') not in ['unknown', 'unavailable'] }}"

      - name: "Inv2 Charger Status Description"
        unique_id: inv2_charger_status_description
        state: >
          {% set status = states('sensor.inv2_charger_status') | int(0) %}
          {% if status == 768 %}Not Charging
          {% elif status == 769 %}Bulk
          {% elif status == 770 %}Absorption
          {% elif status == 771 %}Overcharge
          {% elif status == 772 %}Equalize
          {% elif status == 773 %}Float
          {% elif status == 774 %}No Float
          {% elif status == 775 %}Constant VI
          {% elif status == 776 %}Charger Disabled
          {% elif status == 777 %}Qualifying AC
          {% elif status == 778 %}Qualifying APS
          {% elif status == 779 %}Engaging Charger
          {% elif status == 780 %}Charge Fault
          {% elif status == 781 %}Charger Suspend
          {% elif status == 782 %}AC Good
          {% elif status == 783 %}APS Good
          {% elif status == 784 %}AC Fault
          {% elif status == 785 %}Charge
          {% elif status == 786 %}Absorption Exit Pending
          {% elif status == 787 %}Ground Fault
          {% elif status == 788 %}AC Good Pending
          {% else %}Unknown ({{ status }})
          {% endif %}
        icon: mdi:battery-charging
        availability: "{{ states('sensor.inv2_charger_status') not in ['unknown', 'unavailable'] }}"

      # Grid Support Template Sensors
      - name: "Inv1 Grid Support Description"
        unique_id: inv1_grid_support_description
        state: >
          {% set status = states('sensor.inv1_grid_support') | int(0) %}
          {% if status == 0 %}Disabled
          {% elif status == 1 %}Enabled
          {% else %}Unknown ({{ status }})
          {% endif %}
        icon: mdi:transmission-tower
        availability: "{{ states('sensor.inv1_grid_support') not in ['unknown', 'unavailable'] }}"

      - name: "Inv2 Grid Support Description"
        unique_id: inv2_grid_support_description
        state: >
          {% set status = states('sensor.inv2_grid_support') | int(0) %}
          {% if status == 0 %}Disabled
          {% elif status == 1 %}Enabled
          {% else %}Unknown ({{ status }})
          {% endif %}
        icon: mdi:transmission-tower
        availability: "{{ states('sensor.inv2_grid_support') not in ['unknown', 'unavailable'] }}"

      # Inverter Enabled Template Sensors
      - name: "Inv1 Inverter Enabled Description"
        unique_id: inv1_inverter_enabled_description
        state: >
          {% set status = states('sensor.inv1_inverter_enabled') | int(0) %}
          {% if status == 0 %}Disabled
          {% elif status == 1 %}Enabled
          {% else %}Unknown ({{ status }})
          {% endif %}
        icon: mdi:power
        availability: "{{ states('sensor.inv1_inverter_enabled') not in ['unknown', 'unavailable'] }}"

      - name: "Inv2 Inverter Enabled Description"
        unique_id: inv2_inverter_enabled_description
        state: >
          {% set status = states('sensor.inv2_inverter_enabled') | int(0) %}
          {% if status == 0 %}Disabled
          {% elif status == 1 %}Enabled
          {% else %}Unknown ({{ status }})
          {% endif %}
        icon: mdi:power
        availability: "{{ states('sensor.inv2_inverter_enabled') not in ['unknown', 'unavailable'] }}"

      # Charger Enabled Template Sensors
      - name: "Inv1 Charger Enabled Description"
        unique_id: inv1_charger_enabled_description
        state: >
          {% set status = states('sensor.inv1_charger_enabled') | int(0) %}
          {% if status == 0 %}Disabled
          {% elif status == 1 %}Enabled
          {% else %}Unknown ({{ status }})
          {% endif %}
        icon: mdi:battery-charging-outline
        availability: "{{ states('sensor.inv1_charger_enabled') not in ['unknown', 'unavailable'] }}"

      - name: "Inv2 Charger Enabled Description"
        unique_id: inv2_charger_enabled_description
        state: >
          {% set status = states('sensor.inv2_charger_enabled') | int(0) %}
          {% if status == 0 %}Disabled
          {% elif status == 1 %}Enabled
          {% else %}Unknown ({{ status }})
          {% endif %}
        icon: mdi:battery-charging-outline
        availability: "{{ states('sensor.inv2_charger_enabled') not in ['unknown', 'unavailable'] }}"

      # Sell Enabled Template Sensors
      - name: "Inv1 Sell Enabled Description"
        unique_id: inv1_sell_enabled_description
        state: >
          {% set status = states('sensor.inv1_sell_enabled') | int(0) %}
          {% if status == 0 %}Disabled
          {% elif status == 1 %}Enabled
          {% else %}Unknown ({{ status }})
          {% endif %}
        icon: mdi:cash-multiple
        availability: "{{ states('sensor.inv1_sell_enabled') not in ['unknown', 'unavailable'] }}"

      - name: "Inv2 Sell Enabled Description"
        unique_id: inv2_sell_enabled_description
        state: >
          {% set status = states('sensor.inv2_sell_enabled') | int(0) %}
          {% if status == 0 %}Disabled
          {% elif status == 1 %}Enabled
          {% else %}Unknown ({{ status }})
          {% endif %}
        icon: mdi:cash-multiple
        availability: "{{ states('sensor.inv2_sell_enabled') not in ['unknown', 'unavailable'] }}"

      # Active Faults Flag Template Sensors
      - name: "Inv1 Active Faults Description"
        unique_id: inv1_active_faults_description
        state: >
          {% set status = states('sensor.inv1_active_faults_flag') | int(0) %}
          {% if status == 0 %}No Faults
          {% elif status == 1 %}Active Faults
          {% else %}Unknown ({{ status }})
          {% endif %}
        icon: >
          {% set status = states('sensor.inv1_active_faults_flag') | int(0) %}
          {% if status == 1 %}mdi:alert-circle
          {% else %}mdi:check-circle
          {% endif %}
        availability: "{{ states('sensor.inv1_active_faults_flag') not in ['unknown', 'unavailable'] }}"

      - name: "Inv2 Active Faults Description"
        unique_id: inv2_active_faults_description
        state: >
          {% set status = states('sensor.inv2_active_faults_flag') | int(0) %}
          {% if status == 0 %}No Faults
          {% elif status == 1 %}Active Faults
          {% else %}Unknown ({{ status }})
          {% endif %}
        icon: >
          {% set status = states('sensor.inv2_active_faults_flag') | int(0) %}
          {% if status == 1 %}mdi:alert-circle
          {% else %}mdi:check-circle
          {% endif %}
        availability: "{{ states('sensor.inv2_active_faults_flag') not in ['unknown', 'unavailable'] }}"

      # Active Warnings Flag Template Sensors
      - name: "Inv1 Active Warnings Description"
        unique_id: inv1_active_warnings_description
        state: >
          {% set status = states('sensor.inv1_active_warnings_flag') | int(0) %}
          {% if status == 0 %}No Warnings
          {% elif status == 1 %}Active Warnings
          {% else %}Unknown ({{ status }})
          {% endif %}
        icon: >
          {% set status = states('sensor.inv1_active_warnings_flag') | int(0) %}
          {% if status == 1 %}mdi:alert
          {% else %}mdi:check
          {% endif %}
        availability: "{{ states('sensor.inv1_active_warnings_flag') not in ['unknown', 'unavailable'] }}"

      - name: "Inv2 Active Warnings Description"
        unique_id: inv2_active_warnings_description
        state: >
          {% set status = states('sensor.inv2_active_warnings_flag') | int(0) %}
          {% if status == 0 %}No Warnings
          {% elif status == 1 %}Active Warnings
          {% else %}Unknown ({{ status }})
          {% endif %}
        icon: >
          {% set status = states('sensor.inv2_active_warnings_flag') | int(0) %}
          {% if status == 1 %}mdi:alert
          {% else %}mdi:check
          {% endif %}
        availability: "{{ states('sensor.inv2_active_warnings_flag') not in ['unknown', 'unavailable'] }}"

      # Charge Mode Status Template Sensors
      - name: "Inv1 Charge Mode Description"
        unique_id: inv1_charge_mode_description
        state: >
          {% set status = states('sensor.inv1_charge_mode_status') | int(0) %}
          {% if status == 0 %}Stand Alone
          {% elif status == 1 %}Primary
          {% elif status == 2 %}Secondary
          {% else %}Unknown ({{ status }})
          {% endif %}
        icon: mdi:battery-sync
        availability: "{{ states('sensor.inv1_charge_mode_status') not in ['unknown', 'unavailable'] }}"

      - name: "Inv2 Charge Mode Description"
        unique_id: inv2_charge_mode_description
        state: >
          {% set status = states('sensor.inv2_charge_mode_status') | int(0) %}
          {% if status == 0 %}Stand Alone
          {% elif status == 1 %}Primary
          {% elif status == 2 %}Secondary
          {% else %}Unknown ({{ status }})
          {% endif %}
        icon: mdi:battery-sync
        availability: "{{ states('sensor.inv2_charge_mode_status') not in ['unknown', 'unavailable'] }}"

modbus:
  - name: Modbus_Hub
    type: tcp
    host: *************
    port: 502
    delay: 2
    message_wait_milliseconds: 0
    timeout: 5
    sensors:
      - name: MPPT_DC_Power
        unique_id: MPPT_DC_Power
        slave: 30
        address: 92
        unit_of_measurement: Wh
        input_type: holding
        data_type: uint16
        scale: 1.0
        offset: 0.0
      - name: MPPT_DC_Voltage
        unique_id: MPPT_DC_Voltage
        slave: 30
        address: 0x0058
        unit_of_measurement: V
        input_type: holding
        data_type: int16
        scale: 0.001
        offset: 0.0
        precision: 2
      - name: Home_Load
        unique_id: Home_Load
        slave: 201
        address: 94
        unit_of_measurement: Wh
        input_type: holding
        data_type: uint16
        scale: 1.0
        offset: 0.0
      - name: Grid_AC_Power
        unique_id: Grid_AC_Power
        slave: 201
        address: 82
        unit_of_measurement: Wh
        input_type: holding
        data_type: uint16
        scale: 1.0
        offset: 0.0
        device_class: energy
        state_class: total
      - name: Inv1_Battery_Power
        unique_id: Inv1_Battery_Power
        slave: 10
        address: 0x0054
        unit_of_measurement: Wh
        input_type: holding
        data_type: int16
        scale: 1.0
        offset: 0.0
      - name: Inv2_Battery_Power
        unique_id: Inv2_Battery_Power
        slave: 12
        address: 0x0054
        unit_of_measurement: Wh
        input_type: holding
        data_type: int16
        scale: 1.0
        offset: 0.0
      - name: Battery_Temperature
        unique_id: Battery_Temperature
        slave: 12
        address: 86
        unit_of_measurement: °C
        input_type: holding
        data_type: uint16
        scale: 0.01
        offset: -273.0
      - name: Inv1_Grid_Support
        unique_id: Inv1_Grid_Support
        slave: 10
        slave_count: 0
        scan_interval: 20
        device_class: enum
        address: 435
        data_type: uint16
      - name: Inv2_Grid_Support
        unique_id: Inv2_Grid_Support
        slave: 12
        slave_count: 0
        scan_interval: 20
        device_class: enum
        address: 435
        data_type: uint16
        # Status and Configuration Registers (0x0047-0x004E)
      - name: Inv1_Inverter_Enabled
        unique_id: Inv1_Inverter_Enabled
        slave: 10
        address: 0x0047
        input_type: holding
        data_type: uint16
        scale: 1.0
        offset: 0.0
        device_class: enum
        # 0=Disabled 1=Enabled
      - name: Inv1_Charger_Enabled
        unique_id: Inv1_Charger_Enabled
        slave: 10
        address: 0x0048
        input_type: holding
        data_type: uint16
        scale: 1.0
        offset: 0.0
        device_class: enum
        # 0=Disabled 1=Enabled
      - name: Inv1_Sell_Enabled
        unique_id: Inv1_Sell_Enabled
        slave: 10
        address: 0x0049
        input_type: holding
        data_type: uint16
        scale: 1.0
        offset: 0.0
        device_class: enum
        # 0=Disabled 1=Enabled
      - name: Inv1_Active_Faults_Flag
        unique_id: Inv1_Active_Faults_Flag
        slave: 10
        address: 0x004B
        input_type: holding
        data_type: uint16
        scale: 1.0
        offset: 0.0
        device_class: enum
        # 0=No Faults 1=Active Faults
      - name: Inv1_Active_Warnings_Flag
        unique_id: Inv1_Active_Warnings_Flag
        slave: 10
        address: 0x004C
        input_type: holding
        data_type: uint16
        scale: 1.0
        offset: 0.0
        device_class: enum
        # 0=No Warnings 1=Active Warnings
      - name: Inv1_Charge_Mode_Status
        unique_id: Inv1_Charge_Mode_Status
        slave: 10
        address: 0x004D
        input_type: holding
        data_type: uint16
        scale: 1.0
        offset: 0.0
        device_class: enum
        # 0=Stand alone 1=Primary 2=Secondary
      - name: Inv1_Configuration_Errors
        unique_id: Inv1_Configuration_Errors
        slave: 10
        address: 0x004E
        input_type: holding
        data_type: uint32
        scale: 1.0
        offset: 0.0
      - name: Inv1_Inverter_Status
        unique_id: Inv1_Inverter_Status
        slave: 10
        address: 0x007A
        input_type: holding
        data_type: uint16
        scale: 1.0
        offset: 0.0
        device_class: enum
        # See section 10
      - name: Inv1_Charger_Status
        unique_id: Inv1_Charger_Status
        slave: 10
        address: 0x007B
        input_type: holding
        data_type: uint16
        scale: 1.0
        offset: 0.0
        device_class: enum
        # See section 11
      # Status and Configuration Registers for Inverter 2 (0x0047-0x004E)
      - name: Inv2_Inverter_Enabled
        unique_id: Inv2_Inverter_Enabled
        slave: 12
        address: 0x0047
        input_type: holding
        data_type: uint16
        scale: 1.0
        offset: 0.0
        device_class: enum
        # 0=Disabled 1=Enabled
      - name: Inv2_Charger_Enabled
        unique_id: Inv2_Charger_Enabled
        slave: 12
        address: 0x0048
        input_type: holding
        data_type: uint16
        scale: 1.0
        offset: 0.0
        device_class: enum
        # 0=Disabled 1=Enabled
      - name: Inv2_Sell_Enabled
        unique_id: Inv2_Sell_Enabled
        slave: 12
        address: 0x0049
        input_type: holding
        data_type: uint16
        scale: 1.0
        offset: 0.0
        device_class: enum
        # 0=Disabled 1=Enabled
      - name: Inv2_Active_Faults_Flag
        unique_id: Inv2_Active_Faults_Flag
        slave: 12
        address: 0x004B
        input_type: holding
        data_type: uint16
        scale: 1.0
        offset: 0.0
        device_class: enum
        # 0=No Faults 1=Active Faults
      - name: Inv2_Active_Warnings_Flag
        unique_id: Inv2_Active_Warnings_Flag
        slave: 12
        address: 0x004C
        input_type: holding
        data_type: uint16
        scale: 1.0
        offset: 0.0
        device_class: enum
        # 0=No Warnings 1=Active Warnings
      - name: Inv2_Charge_Mode_Status
        unique_id: Inv2_Charge_Mode_Status
        slave: 12
        address: 0x004D
        input_type: holding
        data_type: uint16
        scale: 1.0
        offset: 0.0
        device_class: enum
        # 0=Stand alone 1=Primary 2=Secondary
      - name: Inv2_Configuration_Errors
        unique_id: Inv2_Configuration_Errors
        slave: 12
        address: 0x004E
        input_type: holding
        data_type: uint32
        scale: 1.0
        offset: 0.0
      - name: Inv2_Inverter_Status
        unique_id: Inv2_Inverter_Status
        slave: 12
        address: 0x007A
        input_type: holding
        data_type: uint16
        scale: 1.0
        offset: 0.0
        device_class: enum
        # See section 10
      - name: Inv2_Charger_Status
        unique_id: Inv2_Charger_Status
        slave: 12
        address: 0x007B
        input_type: holding
        data_type: uint16
        scale: 1.0
        offset: 0.0
        device_class: enum
        # See section 11
        # Grid AC Voltage Sensors (0x0062)
      - name: Inv1_Grid_AC_Voltage
        unique_id: Inv1_Grid_AC_Voltage
        slave: 10
        address: 0x0062
        unit_of_measurement: V
        input_type: holding
        data_type: uint32
        swap: word
        scale: 0.001
        offset: 0.0
        device_class: voltage
        state_class: measurement
        precision: 2
      - name: Inv2_Grid_AC_Voltage
        unique_id: Inv2_Grid_AC_Voltage
        slave: 12
        address: 0x0062
        unit_of_measurement: V
        input_type: holding
        data_type: uint32
        swap: word
        scale: 0.001
        offset: 0.0
        device_class: voltage
        state_class: measurement
        precision: 2
    # Switches for Grid Support with read/write capabilities
    switches:
      - name: Inv1_Grid_Support_Control
        unique_id: Inv1_Grid_Support_Control
        slave: 10
        address: 435
        write_type: holding
        command_on: 1
        command_off: 0
        scan_interval: 20
        verify:
          input_type: holding
          address: 435
          state_on: 1
          state_off: 0
      - name: Inv2_Grid_Support_Control
        unique_id: Inv2_Grid_Support_Control
        slave: 12
        address: 435
        write_type: holding
        command_on: 1
        command_off: 0
        scan_interval: 20
        verify:
          input_type: holding
          address: 435
          state_on: 1
          state_off: 0
