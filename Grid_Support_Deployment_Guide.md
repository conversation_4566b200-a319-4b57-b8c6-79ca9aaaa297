# Grid Support Control Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying the new Grid Support control switches in your Home Assistant instance. These switches enable both read and write operations for Grid Support functionality on inverters with slave IDs 10 and 12.

## ⚠️ Safety Considerations

- **Grid Support controls affect power system behavior** - test during low-load periods
- **Always have a backup plan** to manually control inverters if needed
- **Monitor system behavior** closely during initial testing
- **Understand your local grid requirements** before modifying Grid Support settings

## 1. Pre-Deployment Steps

### 1.1 Backup Current Configuration

```bash
# Create backup of current configuration
cp /config/configuration.yaml /config/configuration.yaml.backup.$(date +%Y%m%d_%H%M%S)

# Backup the specific file we're modifying
cp /config/homeassistant-configurations.yaml /config/homeassistant-configurations.yaml.backup.$(date +%Y%m%d_%H%M%S)
```

### 1.2 Validate YAML Configuration

Before deploying, validate your YAML syntax:

**Option A: Home Assistant Configuration Check**

1. Go to **Developer Tools** > **YAML Configuration**
2. Click **"Check Configuration"**
3. Ensure no errors are reported

**Option B: Command Line Validation**

```bash
# From Home Assistant container or host
ha core check
```

**Option C: Online YAML Validator**

- Copy your configuration to an online YAML validator
- Ensure proper indentation and syntax

### 1.3 Document Current Entity States

Before deployment, record current Grid Support sensor values:

1. Go to **Developer Tools** > **States**
2. Search for `sensor.inv1_grid_support` and `sensor.inv2_grid_support`
3. Note their current values for comparison after deployment

## 2. Deployment Process

### 2.1 Apply Configuration Changes

Your `homeassistant-configurations.yaml` file should now include the new switches section. The configuration adds:

- `switch.inv1_grid_support_control` (slave: 10, address: 435)
- `switch.inv2_grid_support_control` (slave: 12, address: 435)

### 2.2 Restart Home Assistant

**Method A: UI Restart**

1. Go to **Settings** > **System** > **Restart**
2. Click **"Restart Home Assistant"**
3. Wait for system to fully restart (typically 2-3 minutes)

**Method B: Command Line Restart**

```bash
# From Home Assistant host
ha core restart
```

### 2.3 Monitor Restart Process

1. Watch the Home Assistant log during restart:
   - **Settings** > **System** > **Logs**
2. Look for Modbus-related messages
3. Ensure no errors related to the new switch entities

## 3. Entity Verification

### 3.1 Verify New Entities Created

After restart, check that new entities are available:

1. Go to **Settings** > **Devices & Services**
2. Find **"Modbus"** integration
3. Click on it to view entities
4. Confirm these new entities exist:
   - `switch.inv1_grid_support_control`
   - `switch.inv2_grid_support_control`

### 3.2 Check Entity States

1. Go to **Developer Tools** > **States**
2. Search for the new switch entities
3. Verify they show current state (on/off)
4. Check that states match the original sensor values

### 3.3 Review Entity Attributes

Click on each switch entity to verify attributes:

- **Entity ID**: Correct naming convention
- **State**: Should reflect current Grid Support status
- **Attributes**: Should include Modbus-specific information

## 4. UI Configuration

### 4.1 Add to Dashboard (Lovelace UI)

**Method A: Visual Editor**

1. Go to your main dashboard
2. Click **"Edit Dashboard"** (three dots menu)
3. Click **"+ Add Card"**
4. Select **"Entities Card"**
5. Add the new switch entities:
   - `switch.inv1_grid_support_control`
   - `switch.inv2_grid_support_control`
6. Configure card title: "Grid Support Controls"

**Method B: YAML Configuration**
Add this to your dashboard YAML:

```yaml
type: entities
title: Grid Support Controls
entities:
  - entity: switch.inv1_grid_support_control
    name: "Inverter 1 Grid Support"
    icon: mdi:transmission-tower
  - entity: switch.inv2_grid_support_control
    name: "Inverter 2 Grid Support"
    icon: mdi:transmission-tower
  - type: divider
  - entity: sensor.inv1_grid_support
    name: "Inv1 Status (Read-Only)"
  - entity: sensor.inv2_grid_support
    name: "Inv2 Status (Read-Only)"
```

### 4.2 Create Dedicated Grid Support View

For better organization, create a dedicated view:

1. **Edit Dashboard** > **"+ Add View"**
2. **Title**: "Grid Support"
3. **Icon**: `mdi:transmission-tower`
4. Add cards for monitoring and control

## 5. Testing Procedures

### 5.1 Initial Safety Test

**IMPORTANT**: Perform during low-load conditions

1. **Monitor Current State**:
   - Note current Grid Support status on both inverters
   - Check grid frequency and voltage stability

2. **Test Read Functionality**:
   - Verify switch states match sensor readings
   - Confirm values update according to scan_interval (20 seconds)

### 5.2 Write Functionality Test

**Step 1: Single Inverter Test**

1. Choose one inverter for initial testing (recommend Inv1)
2. Toggle the switch: `switch.inv1_grid_support_control`
3. **Wait 20+ seconds** for verification
4. Check that:
   - Switch state reflects the change
   - Original sensor shows updated value
   - System remains stable

**Step 2: Verification Test**

1. Toggle the switch back to original state
2. Verify system returns to previous behavior
3. Monitor for any grid stability issues

**Step 3: Second Inverter Test**

1. Repeat process for `switch.inv2_grid_support_control`
2. Test both individual and coordinated control

### 5.3 Monitoring During Tests

Watch these indicators during testing:

- **Grid frequency stability**
- **Voltage levels**
- **Power flow direction**
- **System logs for errors**
- **Inverter status indicators**

## 6. Troubleshooting

### 6.1 Common Issues

**Issue**: Switch entities not created after restart
**Solution**:

- Check YAML indentation in switches section
- Verify Modbus hub name matches configuration
- Review Home Assistant logs for Modbus errors

**Issue**: Switches show "unavailable"
**Solution**:

- Check Modbus TCP connection (IP: *************, Port: 502)
- Verify slave addresses (10, 12) are responding
- Check network connectivity to Modbus device

**Issue**: Write operations fail
**Solution**:

- Verify holding register 435 supports write operations
- Check if register requires specific value ranges
- Review Modbus device documentation for write permissions

**Issue**: Values don't update after write
**Solution**:

- Increase scan_interval temporarily for testing
- Check verify configuration in switches
- Ensure register address 435 is correct for both read/write

### 6.2 Diagnostic Commands

**Check Modbus Communication**:

```bash
# From Home Assistant logs
grep -i modbus /config/home-assistant.log | tail -20
```

**Monitor Entity States**:

- Use **Developer Tools** > **States** to watch real-time changes
- Enable debug logging for Modbus (see section 6.3)

### 6.3 Enable Debug Logging

Add to `configuration.yaml` for detailed troubleshooting:

```yaml
logger:
  default: warning
  logs:
    homeassistant.components.modbus: debug
    pymodbus: debug
```

## 7. Integration with Automations

### 7.1 Basic Automation Examples

**Coordinated Grid Support Control**:

```yaml
automation:
  - alias: "Enable Grid Support Both Inverters"
    trigger:
      - platform: state
        entity_id: input_boolean.enable_grid_support
        to: 'on'
    action:
      - service: switch.turn_on
        target:
          entity_id:
            - switch.inv1_grid_support_control
            - switch.inv2_grid_support_control

  - alias: "Disable Grid Support Both Inverters"
    trigger:
      - platform: state
        entity_id: input_boolean.enable_grid_support
        to: 'off'
    action:
      - service: switch.turn_off
        target:
          entity_id:
            - switch.inv1_grid_support_control
            - switch.inv2_grid_support_control
```

**Time-Based Grid Support**:

```yaml
automation:
  - alias: "Grid Support Peak Hours"
    trigger:
      - platform: time
        at: "16:00:00"  # Peak demand period
    action:
      - service: switch.turn_on
        target:
          entity_id:
            - switch.inv1_grid_support_control
            - switch.inv2_grid_support_control

  - alias: "Grid Support Off-Peak"
    trigger:
      - platform: time
        at: "22:00:00"  # Off-peak period
    action:
      - service: switch.turn_off
        target:
          entity_id:
            - switch.inv1_grid_support_control
            - switch.inv2_grid_support_control
```

### 7.2 Safety Automations

**Grid Support Status Monitoring**:

```yaml
automation:
  - alias: "Grid Support Status Alert"
    trigger:
      - platform: state
        entity_id:
          - switch.inv1_grid_support_control
          - switch.inv2_grid_support_control
    action:
      - service: notify.mobile_app_your_phone
        data:
          title: "Grid Support Status Changed"
          message: "{{ trigger.entity_id }} changed to {{ trigger.to_state.state }}"
```

## 8. Maintenance and Monitoring

### 8.1 Regular Checks

- **Weekly**: Verify switch states match expected behavior
- **Monthly**: Review automation logs for any failures
- **Quarterly**: Test manual override procedures

### 8.2 Performance Monitoring

Monitor these metrics:

- **Response time**: Time between switch toggle and state change
- **Reliability**: Success rate of write operations
- **System stability**: Grid parameters during state changes

## 9. Rollback Procedure

If issues arise, quickly rollback:

1. **Immediate**: Restore backup configuration

```bash
cp /config/homeassistant-configurations.yaml.backup.YYYYMMDD_HHMMSS /config/homeassistant-configurations.yaml
```

2. **Restart Home Assistant**

3. **Verify**: Original read-only sensors are working

4. **Manual Control**: Use inverter local controls if needed

## 10. Next Steps

After successful deployment:

1. **Document**: Record successful test results
2. **Train**: Ensure operators understand new controls
3. **Integrate**: Add to existing energy management automations
4. **Monitor**: Establish ongoing monitoring procedures

## Support Resources

- **Home Assistant Modbus Documentation**: <https://www.home-assistant.io/integrations/modbus/>
- **Community Forum**: <https://community.home-assistant.io/>
- **Local Configuration**: Review your inverter documentation for Grid Support specifics

---

**Deployment Checklist**:

- [ ] Configuration backed up
- [ ] YAML validated
- [ ] Home Assistant restarted
- [ ] New entities verified
- [ ] UI configured
- [ ] Read functionality tested
- [ ] Write functionality tested (safely)
- [ ] Monitoring established
- [ ] Documentation updated
