# Audit Trail

## Wednesday, July 02, 2025

### Added Human-Readable Status Descriptions for Inverter and Charger Status

**Objective**: Configure Home Assistant to display human-readable descriptions for Modbus register values instead of raw integer values for Inverter Status and Charger Status entities.

**Changes Made**:

1. **Added Template Sensors Section**: Added a new `template:` section to `homeassistant-configurations.yaml` with 4 new template sensors:
   - `Inv1 Inverter Status Description` - Converts raw integer codes to descriptive text for Inverter 1
   - `Inv2 Inverter Status Description` - Converts raw integer codes to descriptive text for Inverter 2
   - `Inv1 Charger Status Description` - Converts raw integer codes to descriptive text for Charger 1
   - `Inv2 Charger Status Description` - Converts raw integer codes to descriptive text for Charger 2

2. **Inverter Status Mappings**: Implemented complete mapping for all 17 inverter status codes from Section 10:
   - 1024=Invert, 1025=AC Pass Through, 1026=APS Only, 1027=Load Sense
   - 1028=Inverter Disabled, 1029=Load Sense Ready, 1030=Engaging Inverter
   - 1031=Invert Fault, 1032=Inverter Standby, 1033=Grid-Tied, 1034=Grid Support
   - 1035=Gen Support, 1036=Sell-to-Grid, 1037=Load Shaving
   - 1038=Grid Frequency Stabilization, 1039=AC Coupling, 1040=Reverse Ibatt

3. **Charger Status Mappings**: Implemented complete mapping for all 21 charger status codes from Section 11:
   - 768=Not Charging, 769=Bulk, 770=Absorption, 771=Overcharge, 772=Equalize
   - 773=Float, 774=No Float, 775=Constant VI, 776=Charger Disabled
   - 777=Qualifying AC, 778=Qualifying APS, 779=Engaging Charger, 780=Charge Fault
   - 781=Charger Suspend, 782=AC Good, 783=APS Good, 784=AC Fault, 785=Charge
   - 786=Absorption Exit Pending, 787=Ground Fault, 788=AC Good Pending

4. **Template Features**:
   - **Error Handling**: Unknown status codes display as "Unknown (code)" for debugging
   - **Availability**: Templates only show valid states when source sensors are available
   - **Icons**: Added appropriate MDI icons (mdi:power-plug for inverters, mdi:battery-charging for chargers)
   - **Unique IDs**: Each template sensor has a unique_id for entity registry management

**Technical Implementation**:

- **Template Syntax**: Used Jinja2 templating with conditional logic for status code mapping
- **State Conversion**: Raw integer values from Modbus sensors converted to descriptive strings
- **Fallback Handling**: Graceful handling of unknown/unavailable states
- **Consistent Naming**: Template sensors follow "InvX Status Description" naming convention

**Benefits**:

- **User-Friendly Interface**: Dashboard displays meaningful status descriptions instead of cryptic integer codes
- **Operational Clarity**: Operators can immediately understand system status without referencing documentation
- **Preserved Raw Data**: Original Modbus sensors remain unchanged for automation and advanced use cases
- **Complete Coverage**: All documented status codes from both Section 10 and Section 11 are mapped
- **Robust Error Handling**: Unknown codes are displayed with their numeric value for troubleshooting

**Usage**:

After Home Assistant restart, the following new entities will be available:

- `sensor.inv1_inverter_status_description`
- `sensor.inv2_inverter_status_description`
- `sensor.inv1_charger_status_description`
- `sensor.inv2_charger_status_description`

These can be used in dashboards, automations, and notifications to provide clear, human-readable status information.

**Outcome**: Successfully implemented comprehensive status description mapping for all inverter and charger status codes, providing intuitive status monitoring while maintaining access to raw data for advanced applications.

### Added Additional Human-Readable Template Sensors for Configuration Registers

**Objective**: Extend the human-readable status descriptions to include additional Modbus configuration and status registers for comprehensive system monitoring.

**Changes Made**:

1. **Added Grid Support Template Sensors** (2 sensors):
   - `Inv1 Grid Support Description` - Converts 0/1 to "Disabled"/"Enabled"
   - `Inv2 Grid Support Description` - Converts 0/1 to "Disabled"/"Enabled"
   - Icon: `mdi:transmission-tower`

2. **Added Inverter Enabled Template Sensors** (2 sensors):
   - `Inv1 Inverter Enabled Description` - Converts 0/1 to "Disabled"/"Enabled"
   - `Inv2 Inverter Enabled Description` - Converts 0/1 to "Disabled"/"Enabled"
   - Icon: `mdi:power`

3. **Added Charger Enabled Template Sensors** (2 sensors):
   - `Inv1 Charger Enabled Description` - Converts 0/1 to "Disabled"/"Enabled"
   - `Inv2 Charger Enabled Description` - Converts 0/1 to "Disabled"/"Enabled"
   - Icon: `mdi:battery-charging-outline`

4. **Added Sell Enabled Template Sensors** (2 sensors):
   - `Inv1 Sell Enabled Description` - Converts 0/1 to "Disabled"/"Enabled"
   - `Inv2 Sell Enabled Description` - Converts 0/1 to "Disabled"/"Enabled"
   - Icon: `mdi:cash-multiple`

5. **Added Active Faults Flag Template Sensors** (2 sensors):
   - `Inv1 Active Faults Description` - Converts 0/1 to "No Faults"/"Active Faults"
   - `Inv2 Active Faults Description` - Converts 0/1 to "No Faults"/"Active Faults"
   - Dynamic Icons: `mdi:check-circle` (no faults) / `mdi:alert-circle` (active faults)

6. **Added Active Warnings Flag Template Sensors** (2 sensors):
   - `Inv1 Active Warnings Description` - Converts 0/1 to "No Warnings"/"Active Warnings"
   - `Inv2 Active Warnings Description` - Converts 0/1 to "No Warnings"/"Active Warnings"
   - Dynamic Icons: `mdi:check` (no warnings) / `mdi:alert` (active warnings)

7. **Added Charge Mode Status Template Sensors** (2 sensors):
   - `Inv1 Charge Mode Description` - Converts 0/1/2 to "Stand Alone"/"Primary"/"Secondary"
   - `Inv2 Charge Mode Description` - Converts 0/1/2 to "Stand Alone"/"Primary"/"Secondary"
   - Icon: `mdi:battery-sync`

**Register Mappings Implemented**:

- **Grid Support (0x01B3)**: 0=Disabled, 1=Enabled
- **Inverter Enabled (0x0047)**: 0=Disabled, 1=Enabled
- **Charger Enabled (0x0048)**: 0=Disabled, 1=Enabled
- **Sell Enabled (0x0049)**: 0=Disabled, 1=Enabled
- **Active Faults Flag (0x004B)**: 0=No Faults, 1=Active Faults
- **Active Warnings Flag (0x004C)**: 0=No Warnings, 1=Active Warnings
- **Charge Mode Status (0x004D)**: 0=Stand Alone, 1=Primary, 2=Secondary

**Enhanced Features**:

- **Dynamic Icons**: Fault and warning sensors use context-aware icons that change based on status
- **Consistent Naming**: All template sensors follow "InvX [Function] Description" pattern
- **Complete Coverage**: Template sensors created for both inverters where applicable
- **Error Handling**: Unknown values display as "Unknown (code)" for debugging
- **Availability Checks**: Templates only show valid states when source sensors are available

**Total Template Sensors Added**: 14 additional sensors (bringing total to 18 template sensors)

**New Entity Names**:

- `sensor.inv1_grid_support_description` / `sensor.inv2_grid_support_description`
- `sensor.inv1_inverter_enabled_description` / `sensor.inv2_inverter_enabled_description`
- `sensor.inv1_charger_enabled_description` / `sensor.inv2_charger_enabled_description`
- `sensor.inv1_sell_enabled_description` / `sensor.inv2_sell_enabled_description`
- `sensor.inv1_active_faults_description` / `sensor.inv2_active_faults_description`
- `sensor.inv1_active_warnings_description` / `sensor.inv2_active_warnings_description`
- `sensor.inv1_charge_mode_description` / `sensor.inv2_charge_mode_description`

**Benefits**:

- **Comprehensive Status Overview**: Complete human-readable status for all major configuration and operational parameters
- **Visual Status Indicators**: Dynamic icons provide immediate visual feedback for fault/warning conditions
- **Operational Clarity**: Clear understanding of system configuration and operational state
- **Dashboard Ready**: All sensors ready for immediate use in Home Assistant dashboards and automations

**Outcome**: Successfully expanded the human-readable status system to cover all major configuration and status registers, providing comprehensive system monitoring with intuitive descriptions and visual indicators.

### Added Grid AC Voltage Sensors for Both Inverters

**Objective**: Add Grid AC Voltage monitoring capability for both inverters to provide real-time AC voltage measurements from the grid connection.

**Changes Made**:

1. **Added Grid AC Voltage Sensors** (2 sensors):
   - `Inv1_Grid_AC_Voltage` - Grid AC voltage measurement for Inverter 1 (slave 10)
   - `Inv2_Grid_AC_Voltage` - Grid AC voltage measurement for Inverter 2 (slave 12)

2. **Register Specification** (from documentation.txt):
   - **Address**: 0x0062 (Grid AC Voltage)
   - **Data Type**: uint32
   - **Access**: Read-only (r)
   - **Unit**: Volts (V)
   - **Scale Factor**: 0.001 (converts raw value to volts)
   - **Offset**: 0.0

3. **Sensor Configuration Details**:
   - **Input Type**: holding (reading from holding registers)
   - **Unit of Measurement**: V (volts)
   - **Device Class**: voltage (for proper Home Assistant categorization)
   - **State Class**: measurement (for historical data and statistics)
   - **Precision**: 2 decimal places (for accurate voltage display)
   - **Unique IDs**: Proper unique identifiers for entity registry management

**Technical Implementation**:

- **Register Address**: 0x0062 (hex format as specified in documentation)
- **Data Handling**: uint32 data type to accommodate full voltage range
- **Scaling**: Raw register values multiplied by 0.001 to convert to actual voltage
- **Precision**: 2 decimal places for voltage readings (e.g., 120.45 V)
- **Device Integration**: Proper device_class and state_class for Home Assistant energy monitoring

**Benefits**:

- **Grid Monitoring**: Real-time monitoring of AC grid voltage for both inverters
- **System Health**: Ability to detect grid voltage fluctuations and anomalies
- **Historical Data**: Voltage trends and statistics available through Home Assistant
- **Automation Ready**: Voltage readings available for automations and alerts
- **Dashboard Integration**: Voltage sensors ready for energy monitoring dashboards

**New Entity Names**:

- `sensor.inv1_grid_ac_voltage`
- `sensor.inv2_grid_ac_voltage`

**Usage Applications**:

- **Grid Quality Monitoring**: Track grid voltage stability and quality
- **System Protection**: Create automations for over/under voltage conditions
- **Performance Analysis**: Correlate system performance with grid voltage
- **Energy Management**: Integrate with energy monitoring dashboards
- **Maintenance Alerts**: Notify of abnormal grid voltage conditions

**Note**: Since Grid AC Voltage is a straightforward voltage measurement (not status codes), no additional template sensors for human-readable descriptions were created. The voltage values are already meaningful as numeric measurements in volts.

**Outcome**: Successfully added comprehensive Grid AC Voltage monitoring for both inverters, providing real-time voltage measurements with proper scaling, precision, and Home Assistant integration for effective grid quality monitoring and system protection.

## Wednesday, July 02, 2025

### Added Modbus Register Definitions from Documentation

**Objective**: Add selected Modbus register definitions (0x0047-0x004E) from documentation.txt to the existing YAML configuration file.

**Changes Made**:

1. **Added Status and Configuration Registers**: Added 7 new register definitions to the sensors section of `homeassistant-configurations.yaml`:
   - `Inverter_Enabled` (0x0047) - uint16, read-only, 0=Disabled 1=Enabled
   - `Charger_Enabled` (0x0048) - uint16, read-only, 0=Disabled 1=Enabled
   - `Sell_Enabled` (0x0049) - uint16, read-only, 0=Disabled 1=Enabled
   - `Active_Faults_Flag` (0x004B) - uint16, read-only, 0=No Faults 1=Active Faults
   - `Active_Warnings_Flag` (0x004C) - uint16, read-only, 0=No Warnings 1=Active Warnings
   - `Charge_Mode_Status` (0x004D) - uint16, read-only, 0=Stand alone 1=Primary 2=Secondary
   - `Configuration_Errors` (0x004E) - uint32, read-only

**Technical Implementation**:

- **Register Addresses**: Used hexadecimal format (0x0047-0x004E) as specified in documentation
- **Data Types**: Configured uint16 for most registers, uint32 for Configuration_Errors as per documentation
- **Access Permissions**: All registers configured as read-only (input_type: holding) matching 'r' designation in documentation
- **Scale and Offset**: Applied scale factor 1.0 and offset 0.0 as specified in documentation
- **Device Class**: Used 'enum' device class for status/flag registers to indicate discrete values
- **Comments**: Added inline comments with value descriptions from documentation
- **Slave ID**: Configured all registers for slave ID 10 (primary inverter)

**Configuration Details**:

- **input_type: holding**: Specifies reading from holding registers
- **Comments included**: Added value descriptions as comments for each register (e.g., "0=Disabled 1=Enabled")
- **Proper YAML formatting**: Maintained consistent indentation and structure with existing configuration
- **Logical grouping**: Added section comment to identify the new register group

**Benefits**:

- Provides monitoring capability for critical inverter status flags
- Enables fault and warning detection through Home Assistant
- Allows tracking of charge mode status and configuration errors
- Follows existing YAML configuration patterns and naming conventions
- Includes comprehensive documentation through inline comments

**Outcome**: Successfully added 7 new Modbus register definitions to the Home Assistant configuration, enabling monitoring of inverter status, charger status, sell status, fault flags, warning flags, charge mode, and configuration errors.

### Duplicated Register Definitions for Second Inverter

**Objective**: Extend monitoring coverage to include the second inverter (slave ID 12) by duplicating the status and configuration register definitions.

**Changes Made**:

1. **Added corresponding registers for Inverter 2**: Duplicated all 7 register definitions with "Inv2_" prefix:
   - `Inv2_Inverter_Enabled` (0x0047) - uint16, read-only, 0=Disabled 1=Enabled
   - `Inv2_Charger_Enabled` (0x0048) - uint16, read-only, 0=Disabled 1=Enabled
   - `Inv2_Sell_Enabled` (0x0049) - uint16, read-only, 0=Disabled 1=Enabled
   - `Inv2_Active_Faults_Flag` (0x004B) - uint16, read-only, 0=No Faults 1=Active Faults
   - `Inv2_Active_Warnings_Flag` (0x004C) - uint16, read-only, 0=No Warnings 1=Active Warnings
   - `Inv2_Charge_Mode_Status` (0x004D) - uint16, read-only, 0=Stand alone 1=Primary 2=Secondary
   - `Inv2_Configuration_Errors` (0x004E) - uint32, read-only

2. **Updated naming convention**: User updated original registers to use "Inv1_" prefix for consistency with existing dual-inverter pattern in the configuration.

**Technical Implementation**:

- **Slave ID**: Configured all new registers for slave ID 12 (second inverter)
- **Register Addresses**: Same addresses (0x0047-0x004E) as first inverter, accessed via different slave ID
- **Naming Convention**: Used "Inv2_" prefix to match existing pattern (Inv1_Battery_Power, Inv2_Battery_Power, etc.)
- **Identical Configuration**: Same data types, scale factors, offsets, and device classes as first inverter
- **Unique IDs**: Each sensor has unique_id matching the sensor name for proper entity identification

**Benefits**:

- **Complete Coverage**: Now monitors status registers for both inverters in the dual-inverter system
- **Consistent Naming**: Follows established "Inv1_"/"Inv2_" naming convention used throughout the configuration
- **Parallel Monitoring**: Enables comparison of status between the two inverters
- **Fault Detection**: Provides fault and warning monitoring for both inverters independently
- **Operational Visibility**: Shows charge mode and configuration status for each inverter

**Outcome**: Successfully extended monitoring to cover both inverters with 14 total status register sensors (7 per inverter), providing comprehensive visibility into the dual-inverter system's operational status, fault conditions, and configuration state.

### Added Additional Status Registers (0x007A-0x007B)

**Objective**: Add two additional status registers for both inverters to provide enhanced monitoring capabilities.

**Changes Made**:

1. **Added Inverter Status registers for both inverters**:
   - `Inv1_Inverter_Status` (0x007A) - uint16, read-only, references section 10
   - `Inv2_Inverter_Status` (0x007A) - uint16, read-only, references section 10

2. **Added Charger Status registers for both inverters**:
   - `Inv1_Charger_Status` (0x007B) - uint16, read-only, references section 11
   - `Inv2_Charger_Status` (0x007B) - uint16, read-only, references section 11

**Technical Implementation**:

- **Register Addresses**: Used hexadecimal format (0x007A, 0x007B) as specified in documentation
- **Data Types**: Configured as uint16 for both registers as per documentation
- **Access Permissions**: Configured as read-only (input_type: holding) matching 'r' designation
- **Scale and Offset**: Applied scale factor 1.0 and offset 0.0 as specified
- **Device Class**: Used 'enum' device class for status registers indicating discrete values
- **Documentation References**: Added comments referencing sections 10 and 11 as noted in documentation
- **Dual-Inverter Coverage**: Added registers for both slave IDs (10 and 12)
- **Naming Convention**: Followed established "Inv1_"/"Inv2_" prefix pattern

**Configuration Details**:

- **input_type: holding**: Specifies reading from holding registers
- **Comments included**: Added documentation section references for detailed status interpretations
- **Proper YAML formatting**: Maintained consistent indentation and structure
- **Logical placement**: Added after existing status registers for each inverter

**Benefits**:

- **Enhanced Status Monitoring**: Provides detailed inverter and charger status information beyond basic enabled/disabled flags
- **Comprehensive Coverage**: Monitors both inverters' detailed status registers
- **Documentation Integration**: References specific documentation sections for status value interpretation
- **Consistent Implementation**: Follows established patterns for dual-inverter monitoring

**Outcome**: Successfully added 4 additional status register sensors (2 per inverter), bringing the total to 18 status monitoring sensors across both inverters, providing comprehensive visibility into inverter and charger operational status with detailed state information.

## Tuesday, June 24, 2025

### Grid Support Sensors - Added Write Capabilities

**Objective**: Transform the two Grid Support sensors from read-only to read-write capable while maintaining their current functionality.

**Changes Made**:

1. **Preserved existing read-only sensors**: Kept the original `Inv1_Grid_Support` and `Inv2_Grid_Support` sensors intact to maintain backward compatibility and existing read functionality.

2. **Added new switch entities for write capabilities**:
   - `Inv1_Grid_Support_Control` (slave: 10, address: 435)
   - `Inv2_Grid_Support_Control` (slave: 12, address: 435)

**Technical Implementation**:

- **Entity Type**: Added new `switches` section to the Modbus configuration
- **Write Type**: Used `holding` register type for write operations
- **Commands**: Configured `command_on: 1` and `command_off: 0` for binary control
- **Verification**: Added `verify` configuration to read back the written values for confirmation
- **Scan Interval**: Maintained 20-second scan interval consistent with original sensors
- **Address**: Used same address (435) as the original sensors for both read and write operations

**Configuration Details**:

- **write_type: holding**: Enables writing to holding registers using the `write_register` function
- **verify section**: Ensures written values are confirmed by reading back from the same register
- **state_on/state_off**: Defines expected values (1/0) when reading back the register state

**Benefits**:

- Maintains existing read-only functionality for backward compatibility
- Adds new write-capable entities for control operations
- Follows Home Assistant Modbus integration best practices
- Includes verification to ensure write operations are successful
- Uses proper YAML formatting and indentation

**Outcome**: Successfully added write capabilities while preserving existing read functionality. The configuration now supports both monitoring (via sensors) and control (via switches) of the Grid Support functionality on both inverters.

### Grid Support Deployment Guide Created

**Objective**: Provide comprehensive deployment instructions for implementing the new Grid Support switch controls.

**File Created**: `Grid_Support_Deployment_Guide.md`

**Content Includes**:

1. **Pre-deployment steps**: YAML validation, backup procedures, current state documentation
2. **Deployment process**: Step-by-step restart and configuration application
3. **Entity verification**: Confirming new switch entities are properly created
4. **UI configuration**: Dashboard setup with both visual editor and YAML methods
5. **Testing procedures**: Safe testing protocols for Grid Support toggle functionality
6. **Troubleshooting**: Common issues and diagnostic procedures
7. **Automation integration**: Example automations for coordinated control
8. **Safety considerations**: Important warnings about Grid Support control impacts
9. **Maintenance procedures**: Ongoing monitoring and rollback procedures

**Key Safety Features**:

- Emphasis on testing during low-load conditions
- Backup and rollback procedures
- Monitoring guidelines for system stability
- Debug logging configuration for troubleshooting

**Outcome**: Created comprehensive deployment guide suitable for users with basic Home Assistant knowledge, including all necessary safety considerations and step-by-step procedures for successful implementation.
